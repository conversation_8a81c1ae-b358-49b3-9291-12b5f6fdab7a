package converts

import (
	"math"
	"math/bits"
	"unsafe"
)

// 性能优化：预计算的常量，避免重复计算
const (
	maxFloat32 = math.MaxFloat32
	minInt64   = math.MinInt64
	maxInt64   = math.MaxInt64
	maxUint64  = math.MaxUint64
)

// 内联优化：快速零值检查
func isZero[T Numeric | Complex](v T) bool {
	return v == 0
}

// 内联优化：快速非零检查
func isNonZero[T Numeric | Complex](v T) bool {
	return v != 0
}

// 文本类型到字符串的零拷贝转换 - 高性能优化版本
func textualToString[S Textual](s S) string {
	switch s := any(s).(type) {
	case string:
		return s
	case []byte:
		if len(s) == 0 {
			return ""
		}
		return unsafe.String(unsafe.SliceData(s), len(s))
	case []rune:
		return string(s)
	default:
		return ""
	}
}

// 字符串到文本类型的零拷贝转换 - 高性能优化版本
func stringToTextual[D Textual](s string) D {
	var zero D
	switch any(zero).(type) {
	case string:
		return any(s).(D)
	case []byte:
		if len(s) == 0 {
			return zero
		}
		return any(unsafe.Slice(unsafe.StringData(s), len(s))).(D)
	case []rune:
		return any([]rune(s)).(D)
	default:
		return zero
	}
}

func isInt[T Numeric]() bool {
	var zero T
	switch any(zero).(type) {
	case int, int8, int16, int32, int64:
		return true
	default:
		return false
	}
}

func isUint[T Numeric]() bool {
	var zero T
	switch any(zero).(type) {
	case uint, uint8, uint16, uint32, uint64, uintptr:
		return true
	default:
		return false
	}
}

func isFloat[T Numeric]() bool {
	var zero T
	switch any(zero).(type) {
	case float32, float64:
		return true
	default:
		return false
	}
}

func getBitSize[T Numeric | Complex]() int {
	var zero T
	switch any(zero).(type) {
	case int8, uint8:
		return 8
	case int16, uint16:
		return 16
	case int32, uint32, float32:
		return 32
	case int64, uint64, float64, complex64:
		return 64
	case complex128:
		return 128
	case int, uint, uintptr:
		return bits.UintSize
	default:
		return 0
	}
}

func checkOverflow[T Numeric](src float64, bitSize int) bool {
	if math.IsNaN(src) || math.IsInf(src, 0) {
		return true
	}

	switch {
	case isInt[T]():
		// 处理64位及以上的情况，避免位移溢出
		if bitSize >= 64 {
			return src != math.Trunc(src) || src < math.MinInt64 || src > math.MaxInt64
		}
		cutoff := float64(int64(1) << (bitSize - 1))
		return src != math.Trunc(src) || src < -cutoff || src >= cutoff

	case isUint[T]():
		if bitSize >= 64 {
			return src < 0 || src != math.Trunc(src) || src > math.MaxUint64
		}
		max := float64(uint64(1)<<bitSize - 1)
		return src < 0 || src != math.Trunc(src) || src > max

	case isFloat[T]():
		return bitSize == 32 && math.Abs(src) > math.MaxFloat32
	}
	return true
}
