package converts

import (
	"strconv"
)

type (
	Boolean interface {
		~bool
	}

	Numeric interface {
		~int | ~int8 | ~int16 | ~int32 | ~int64 | ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~uintptr | ~float32 | ~float64
	}

	Complex interface {
		~complex64 | ~complex128
	}

	Textual interface {
		~string | ~[]byte | ~[]rune
	}
)

// ====================================================================================================================

func numericToBoolean[S Numeric, D Boolean](src S) (D, error) {
	return D(isNonZero(src)), nil
}

func complexToBoolean[S Complex, D Boolean](src S) (D, error) {
	return D(isNonZero(src)), nil
}

func textualToBoolean[S Textual, D Boolean](src S) (D, error) {
	if textualToString(src) != "" {
		return D(true), nil
	}
	return D(false), nil
}

func GenericToBoolean[D Boolean](src any) (D, error) {
	switch s := src.(type) {
	case bool:
		return D(s), nil

	// 数值类型 - 类型安全的单独处理
	case int:
		return numericToBoolean[int, D](s)
	case int8:
		return numericToBoolean[int8, D](s)
	case int16:
		return numericToBoolean[int16, D](s)
	case int32:
		return numericToBoolean[int32, D](s)
	case int64:
		return numericToBoolean[int64, D](s)
	case uint:
		return numericToBoolean[uint, D](s)
	case uint8:
		return numericToBoolean[uint8, D](s)
	case uint16:
		return numericToBoolean[uint16, D](s)
	case uint32:
		return numericToBoolean[uint32, D](s)
	case uint64:
		return numericToBoolean[uint64, D](s)
	case uintptr:
		return numericToBoolean[uintptr, D](s)
	case float32:
		return numericToBoolean[float32, D](s)
	case float64:
		return numericToBoolean[float64, D](s)

	// 复数类型
	case complex64:
		return complexToBoolean[complex64, D](s)
	case complex128:
		return complexToBoolean[complex128, D](s)

	// 文本类型
	case string:
		return textualToBoolean[string, D](s)
	case []byte:
		return textualToBoolean[[]byte, D](s)
	case []rune:
		return textualToBoolean[[]rune, D](s)

	default:
		return D(false), ErrUnsupported
	}
}

// ====================================================================================================================

func booleanToNumeric[S Boolean, D Numeric](src S) (D, error) {
	if bool(src) {
		return D(1), nil
	}
	return D(0), nil
}

func numericToNumeric[S Numeric, D Numeric](src S) (D, error) {
	if checkOverflow[D](float64(src), getBitSize[D]()) {
		return D(0), ErrOverflow
	}
	return D(src), nil
}

func complexToNumeric[S Complex, D Numeric](src S) (D, error) {
	return D(real(complex128(src))), nil
}

func textualToNumeric[S Textual, D Numeric](src S) (D, error) {
	str := textualToString(src)
	switch {
	case isInt[D]():
		if i, err := strconv.ParseInt(str, 10, getBitSize[D]()); err == nil {
			return D(i), nil
		} else {
			return D(0), err
		}
	case isUint[D]():
		if u, err := strconv.ParseUint(str, 10, getBitSize[D]()); err == nil {
			return D(u), nil
		} else {
			return D(0), err
		}
	case isFloat[D]():
		if f, err := strconv.ParseFloat(str, getBitSize[D]()); err == nil {
			return D(f), nil
		} else {
			return D(0), err
		}
	}
	return D(0), ErrUnsupported
}

func GenericToNumeric[D Numeric](src any) (D, error) {
	switch s := src.(type) {
	case bool:
		return booleanToNumeric[bool, D](s)

	// 数值类型 - 类型安全的单独处理
	case int:
		return numericToNumeric[int, D](s)
	case int8:
		return numericToNumeric[int8, D](s)
	case int16:
		return numericToNumeric[int16, D](s)
	case int32:
		return numericToNumeric[int32, D](s)
	case int64:
		return numericToNumeric[int64, D](s)
	case uint:
		return numericToNumeric[uint, D](s)
	case uint8:
		return numericToNumeric[uint8, D](s)
	case uint16:
		return numericToNumeric[uint16, D](s)
	case uint32:
		return numericToNumeric[uint32, D](s)
	case uint64:
		return numericToNumeric[uint64, D](s)
	case uintptr:
		return numericToNumeric[uintptr, D](s)
	case float32:
		return numericToNumeric[float32, D](s)
	case float64:
		return numericToNumeric[float64, D](s)

	// 复数类型
	case complex64:
		return complexToNumeric[complex64, D](s)
	case complex128:
		return complexToNumeric[complex128, D](s)

	// 文本类型
	case string:
		return textualToNumeric[string, D](s)
	case []byte:
		return textualToNumeric[[]byte, D](s)
	case []rune:
		return textualToNumeric[[]rune, D](s)

	default:
		return D(0), ErrUnsupported
	}
}

// ====================================================================================================================
func booleanToComplex[S Boolean, D Complex](src S) (D, error) {
	if bool(src) {
		return D(complex(1, 0)), nil
	}
	return D(complex(0, 0)), nil
}

func numericToComplex[S Numeric, D Complex](src S) (D, error) {
	return D(complex(float64(src), 0)), nil
}

func complexToComplex[S Complex, D Complex](src S) (D, error) {
	return D(src), nil
}

func textualToComplex[S Textual, D Complex](src S) (D, error) {
	if c, err := strconv.ParseComplex(textualToString(src), getBitSize[D]()); err == nil {
		return D(c), nil
	} else {
		return D(complex(0, 0)), err
	}
}

func GenericToComplex[D Complex](src any) (D, error) {
	switch s := src.(type) {
	case bool:
		return booleanToComplex[bool, D](s)

	// 数值类型 - 类型安全的单独处理
	case int:
		return numericToComplex[int, D](s)
	case int8:
		return numericToComplex[int8, D](s)
	case int16:
		return numericToComplex[int16, D](s)
	case int32:
		return numericToComplex[int32, D](s)
	case int64:
		return numericToComplex[int64, D](s)
	case uint:
		return numericToComplex[uint, D](s)
	case uint8:
		return numericToComplex[uint8, D](s)
	case uint16:
		return numericToComplex[uint16, D](s)
	case uint32:
		return numericToComplex[uint32, D](s)
	case uint64:
		return numericToComplex[uint64, D](s)
	case uintptr:
		return numericToComplex[uintptr, D](s)
	case float32:
		return numericToComplex[float32, D](s)
	case float64:
		return numericToComplex[float64, D](s)

	// 复数类型
	case complex64:
		return complexToComplex[complex64, D](s)
	case complex128:
		return complexToComplex[complex128, D](s)

	// 文本类型
	case string:
		return textualToComplex[string, D](s)
	case []byte:
		return textualToComplex[[]byte, D](s)
	case []rune:
		return textualToComplex[[]rune, D](s)

	default:
		return D(complex(0, 0)), ErrUnsupported
	}
}

// ====================================================================================================================

func booleanToTextual[S Boolean, D Textual](src S) (D, error) {
	return D(strconv.FormatBool(bool(src))), nil
}

func numericToTextual[S Numeric, D Textual](src S) (D, error) {
	switch {
	case isInt[S]():
		return D(strconv.FormatInt(int64(src), 10)), nil
	case isUint[S]():
		return D(strconv.FormatUint(uint64(src), 10)), nil
	case isFloat[S]():
		return D(strconv.FormatFloat(float64(src), 'f', -1, getBitSize[S]())), nil
	}
	return D(""), nil
}

func complexToTextual[S Complex, D Textual](src S) (D, error) {
	return D(strconv.FormatComplex(complex128(src), 'f', -1, 64)), nil
}

func textualToTextual[S Textual, D Textual](src S) (D, error) {
	return stringToTextual[D](textualToString(src)), nil
}

func GenericToTextual[D Textual](src any) (D, error) {
	switch s := src.(type) {
	case bool:
		return booleanToTextual[bool, D](s)

	// 数值类型 - 类型安全的单独处理
	case int:
		return numericToTextual[int, D](s)
	case int8:
		return numericToTextual[int8, D](s)
	case int16:
		return numericToTextual[int16, D](s)
	case int32:
		return numericToTextual[int32, D](s)
	case int64:
		return numericToTextual[int64, D](s)
	case uint:
		return numericToTextual[uint, D](s)
	case uint8:
		return numericToTextual[uint8, D](s)
	case uint16:
		return numericToTextual[uint16, D](s)
	case uint32:
		return numericToTextual[uint32, D](s)
	case uint64:
		return numericToTextual[uint64, D](s)
	case uintptr:
		return numericToTextual[uintptr, D](s)
	case float32:
		return numericToTextual[float32, D](s)
	case float64:
		return numericToTextual[float64, D](s)

	// 复数类型
	case complex64:
		return complexToTextual[complex64, D](s)
	case complex128:
		return complexToTextual[complex128, D](s)

	// 文本类型
	case string:
		return textualToTextual[string, D](s)
	case []byte:
		return textualToTextual[[]byte, D](s)
	case []rune:
		return textualToTextual[[]rune, D](s)

	default:
		return D(""), ErrUnsupported
	}
}
