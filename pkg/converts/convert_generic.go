package converts

import (
	"strconv"
)

type (
	Boolean interface {
		~bool
	}

	Numeric interface {
		~int | ~int8 | ~int16 | ~int32 | ~int64 | ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~uintptr | ~float32 | ~float64
	}

	Complex interface {
		~complex64 | ~complex128
	}

	Textual interface {
		~string | ~[]byte | ~[]rune
	}
)

// ====================================================================================================================

func booleanToBoolean[S Boolean, D Boolean](src S) (D, error) {
	return D(src), nil
}

func numericToBoolean[S Numeric, D Boolean](src S) (D, error) {
	if src != 0 {
		return D(true), nil
	}
	return D(false), nil
}

func complexToBoolean[S Complex, D Boolean](src S) (D, error) {
	if src != 0 {
		return D(true), nil
	}
	return D(false), nil
}

func textualToBoolean[S Textual, D Boolean](src S) (D, error) {
	if textualToString(src) != "" {
		return D(true), nil
	}
	return D(false), nil
}

func GenericToBoolean[D Boolean](src any) (D, error) {
	switch s := any(src).(type) {
	case bool:
		return booleanToBoolean[bool, D](s)
	case int, int8, int16, int32, int64:
		return numericToBoolean[int64, D](s.(int64))
	case uint, uint8, uint16, uint32, uint64, uintptr:
		return numericToBoolean[uint64, D](s.(uint64))
	case float32, float64:
		return numericToBoolean[float64, D](s.(float64))
	case complex64, complex128:
		return complexToBoolean[complex128, D](s.(complex128))
	case string:
		return textualToBoolean[string, D](s)
	case []byte:
		return textualToBoolean[[]byte, D](s)
	case []rune:
		return textualToBoolean[[]rune, D](s)
	default:
		return D(false), ErrUnsupported
	}
}

// ====================================================================================================================

func booleanToNumeric[S Boolean, D Numeric](src S) (D, error) {
	if bool(src) {
		return D(1), nil
	}
	return D(0), nil
}

func numericToNumeric[S Numeric, D Numeric](src S) (D, error) {
	if checkOverflow[D](float64(src), getBitSize[D]()) {
		return D(0), ErrOverflow
	}
	return D(src), nil
}

func complexToNumeric[S Complex, D Numeric](src S) (D, error) {
	return D(real(complex128(src))), nil
}

func textualToNumeric[S Textual, D Numeric](src S) (D, error) {
	switch {
	case isInt[D]():
		if i, err := strconv.ParseInt(textualToString(src), 10, getBitSize[D]()); err == nil {
			return D(i), nil
		}
	case isUint[D]():
		if u, err := strconv.ParseUint(textualToString(src), 10, getBitSize[D]()); err == nil {
			return D(u), nil
		}
	case isFloat[D]():
		if f, err := strconv.ParseFloat(textualToString(src), getBitSize[D]()); err == nil {
			return D(f), nil
		}
	}
	return D(0), nil
}

func GenericToNumeric[D Numeric](src any) (D, error) {
	switch s := any(src).(type) {
	case bool:
		return booleanToNumeric[bool, D](s)
	case int, int8, int16, int32, int64:
		return numericToNumeric[int64, D](s.(int64))
	case uint, uint8, uint16, uint32, uint64, uintptr:
		return numericToNumeric[uint64, D](s.(uint64))
	case float32, float64:
		return numericToNumeric[float64, D](s.(float64))
	case complex64, complex128:
		return complexToNumeric[complex128, D](s.(complex128))
	case string:
		return textualToNumeric[string, D](s)
	case []byte:
		return textualToNumeric[[]byte, D](s)
	case []rune:
		return textualToNumeric[[]rune, D](s)
	default:
		return D(0), ErrUnsupported
	}
}

// ====================================================================================================================
func booleanToComplex[S Boolean, D Complex](src S) (D, error) {
	if bool(src) {
		return D(complex(1, 0)), nil
	}
	return D(complex(0, 0)), nil
}

func numericToComplex[S Numeric, D Complex](src S) (D, error) {
	return D(complex(float64(src), 0)), nil
}

func complexToComplex[S Complex, D Complex](src S) (D, error) {
	return D(src), nil
}

func textualToComplex[S Textual, D Complex](src S) (D, error) {
	if i, err := strconv.ParseComplex(textualToString(src), getBitSize[D]()); err == nil {
		return D(i), nil
	}
	return D(complex(0, 0)), nil
}

func GenericToComplex[D Complex](src any) (D, error) {
	switch s := any(src).(type) {
	case bool:
		return booleanToComplex[bool, D](s)
	case int, int8, int16, int32, int64:
		return numericToComplex[int64, D](s.(int64))
	case uint, uint8, uint16, uint32, uint64, uintptr:
		return numericToComplex[uint64, D](s.(uint64))
	case float32, float64:
		return numericToComplex[float64, D](s.(float64))
	case complex64, complex128:
		return complexToComplex[complex128, D](s.(complex128))
	case string:
		return textualToComplex[string, D](s)
	case []byte:
		return textualToComplex[[]byte, D](s)
	case []rune:
		return textualToComplex[[]rune, D](s)
	default:
		return D(complex(0, 0)), ErrUnsupported
	}
}

// ====================================================================================================================

func booleanToTextual[S Boolean, D Textual](src S) (D, error) {
	return D(strconv.FormatBool(bool(src))), nil
}

func numericToTextual[S Numeric, D Textual](src S) (D, error) {
	switch {
	case isInt[S]():
		return D(strconv.FormatInt(int64(src), 10)), nil
	case isUint[S]():
		return D(strconv.FormatUint(uint64(src), 10)), nil
	case isFloat[S]():
		return D(strconv.FormatFloat(float64(src), 'f', -1, getBitSize[S]())), nil
	}
	return D(""), nil
}

func complexToTextual[S Complex, D Textual](src S) (D, error) {
	return D(strconv.FormatComplex(complex128(src), 'f', -1, 64)), nil
}

func textualToTextual[S Textual, D Textual](src S) (D, error) {
	return stringToTextual[D](textualToString(src)), nil
}

func GenericToTextual[D Textual](src any) (D, error) {
	switch s := any(src).(type) {
	case bool:
		return booleanToTextual[bool, D](s)
	case int, int8, int16, int32, int64:
		return numericToTextual[int64, D](s.(int64))
	case uint, uint8, uint16, uint32, uint64, uintptr:
		return numericToTextual[uint64, D](s.(uint64))
	case float32, float64:
		return numericToTextual[float64, D](s.(float64))
	case complex64, complex128:
		return complexToTextual[complex128, D](s.(complex128))
	case string:
		return textualToTextual[string, D](s)
	case []byte:
		return textualToTextual[[]byte, D](s)
	case []rune:
		return textualToTextual[[]rune, D](s)
	default:
		return D(""), ErrUnsupported
	}
}
